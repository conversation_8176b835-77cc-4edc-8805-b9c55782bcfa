{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "migrate:workflow": "node scripts/run-workflow-migration.js"}, "dependencies": {"@socket.io/admin-ui": "^0.5.1", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "docx": "^9.5.0", "fs": "0.0.1-security", "path": "^0.12.7", "dotenv": "^16.5.0", "express": "^4.21.2", "express-fileupload": "^1.5.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "puppeteer": "^22.15.0", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "umzug": "^3.5.0"}, "devDependencies": {"nodemon": "^3.1.9", "sequelize-cli": "^6.6.2"}}