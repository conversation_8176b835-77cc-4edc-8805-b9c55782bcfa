const db = require('../../models');
const AuditActivity = db.AuditActivity;
const User = db.User;
const AuditMission = db.AuditMission;
const AuditConstat = db.AuditConstat; // Add this to access AuditConstats
const { v4: uuidv4 } = require('uuid');

// Get all audit activities
const getAllAuditActivities = async (req, res) => {
  try {
    const auditActivities = await AuditActivity.findAll({
      include: [
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        },
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name']
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      data: auditActivities
    });
  } catch (error) {
    console.error('Error fetching audit activities:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit activities',
      error: error.message
    });
  }
};

// Get audit activities by mission ID
const getAuditActivitiesByMissionId = async (req, res) => {
  try {
    const { missionId } = req.params;
    
    const auditActivities = await AuditActivity.findAll({
      where: { auditMissionID: missionId },
      include: [
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      data: auditActivities
    });
  } catch (error) {
    console.error('Error fetching audit activities by mission ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit activities',
      error: error.message
    });
  }
};

// Create a new audit activity
const createAuditActivity = async (req, res) => {
  try {
    const {
      name,
      auditMissionID,
      responsable,
      status,
      datedebut,
      datefin,
      chargedetravailestimee,
      chargedetravaileffective,
      objectif,
      depense
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit activity'
      });
    }
    
    if (!auditMissionID) {
      return res.status(400).json({
        success: false,
        message: 'Audit mission ID is required'
      });
    }
    
    // Validate chargedetravailestimee
    if (chargedetravailestimee !== undefined && chargedetravailestimee !== null) {
      if (!Number.isInteger(Number(chargedetravailestimee))) {
        return res.status(400).json({
          success: false,
          message: 'Estimated workload must be an integer'
        });
      }
    }

    // Validate chargedetravaileffective
    if (chargedetravaileffective !== undefined && chargedetravaileffective !== null) {
      if (!Number.isInteger(Number(chargedetravaileffective))) {
        return res.status(400).json({
          success: false,
          message: 'Effective workload must be an integer'
        });
      }
    }

    // Validate depense
    if (depense !== undefined && depense !== null) {
      const parsedDepense = Number(depense);
      if (isNaN(parsedDepense) || parsedDepense < 0) {
        return res.status(400).json({
          success: false,
          message: 'Expense must be a valid non-negative number'
        });
      }
    }
    
    // Check if audit mission exists
    const auditMission = await AuditMission.findByPk(auditMissionID);
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    // Create the audit activity
    const auditActivity = await AuditActivity.create({
      id: `AA_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      auditMissionID,
      responsable,
      status: status || 'Not Started',
      datedebut,
      datefin,
      chargedetravailestimee: chargedetravailestimee !== undefined ? Number(chargedetravailestimee) : null,
      chargedetravaileffective: chargedetravaileffective !== undefined ? Number(chargedetravaileffective) : null,
      objectif,
      depense: depense !== undefined ? Number(depense) : null
    });

    return res.status(201).json({
      success: true,
      message: 'Audit activity created successfully',
      data: auditActivity
    });
  } catch (error) {
    console.error('Error creating audit activity:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit activity',
      error: error.message
    });
  }
};

// Get audit activity by ID
const getAuditActivityById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditActivity = await AuditActivity.findByPk(id, {
      include: [
        {
          model: User,
          as: 'responsableUser',
          attributes: ['id', 'username', 'email']
        },
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!auditActivity) {
      return res.status(404).json({
        success: false,
        message: 'Audit activity not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: auditActivity
    });
  } catch (error) {
    console.error('Error fetching audit activity:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit activity',
      error: error.message
    });
  }
};

// Update audit activity
const updateAuditActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      auditMissionID,
      responsable,
      status,
      datedebut,
      datefin,
      chargedetravailestimee,
      chargedetravaileffective,
      objectif,
      depense
    } = req.body;
    
    const auditActivity = await AuditActivity.findByPk(id);
    
    if (!auditActivity) {
      return res.status(404).json({
        success: false,
        message: 'Audit activity not found'
      });
    }
    
    // If auditMissionID is being changed, check if the new mission exists
    if (auditMissionID && auditMissionID !== auditActivity.auditMissionID) {
      const auditMission = await AuditMission.findByPk(auditMissionID);
      if (!auditMission) {
        return res.status(404).json({
          success: false,
          message: 'New audit mission not found'
        });
      }
    }
    
    // Validate chargedetravailestimee
    if (chargedetravailestimee !== undefined && chargedetravailestimee !== null) {
      if (!Number.isInteger(Number(chargedetravailestimee))) {
        return res.status(400).json({
          success: false,
          message: 'Estimated workload must be an integer'
        });
      }
    }

    // Validate chargedetravaileffective
    if (chargedetravaileffective !== undefined && chargedetravaileffective !== null) {
      if (!Number.isInteger(Number(chargedetravaileffective))) {
        return res.status(400).json({
          success: false,
          message: 'Effective workload must be an integer'
        });
      }
    }

    // Validate depense
    if (depense !== undefined && depense !== null) {
      const parsedDepense = Number(depense);
      if (isNaN(parsedDepense) || parsedDepense < 0) {
        return res.status(400).json({
          success: false,
          message: 'Expense must be a valid non-negative number'
        });
      }
    }
    
    // Update the audit activity
    await auditActivity.update({
      name: name || auditActivity.name,
      auditMissionID: auditMissionID || auditActivity.auditMissionID,
      responsable: responsable !== undefined ? responsable : auditActivity.responsable,
      status: status !== undefined ? status : auditActivity.status,
      datedebut: datedebut !== undefined ? datedebut : auditActivity.datedebut,
      datefin: datefin !== undefined ? datefin : auditActivity.datefin,
      chargedetravailestimee: chargedetravailestimee !== undefined ? Number(chargedetravailestimee) : auditActivity.chargedetravailestimee,
      chargedetravaileffective: chargedetravaileffective !== undefined ? Number(chargedetravaileffective) : auditActivity.chargedetravaileffective,
      objectif: objectif !== undefined ? objectif : auditActivity.objectif,
      depense: depense !== undefined ? Number(depense) : auditActivity.depense
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit activity updated successfully',
      data: auditActivity
    });
  } catch (error) {
    console.error('Error updating audit activity:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit activity',
      error: error.message
    });
  }
};

// Delete audit activity
const deleteAuditActivity = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditActivity = await AuditActivity.findByPk(id);
    
    if (!auditActivity) {
      return res.status(404).json({
        success: false,
        message: 'Audit activity not found'
      });
    }
    
    // Delete related AuditConstats first
    await AuditConstat.destroy({
      where: { auditActivityID: id }
    });
    
    // Now delete the AuditActivity
    await auditActivity.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Audit activity and related constats deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit activity:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit activity',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditActivities,
  getAuditActivitiesByMissionId,
  createAuditActivity,
  getAuditActivityById,
  updateAuditActivity,
  deleteAuditActivity
};