import React, { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2, Link, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { getApiBaseUrl } from "@/utils/api-config";

export function AuditTestFicheAttachmentsSection({ ficheTest }) {
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({ url: "", description: "" });

  const API_BASE_URL = getApiBaseUrl();
  const businessDocInputRef = useRef(null);

  const fetchAttachments = useCallback(async () => {
    if (!ficheTest?.id) return;

    try {
      setIsLoading(true);
      const timestamp = new Date().getTime();

      const [businessDocsResponse, externalRefsResponse] = await Promise.all([
        axios.get(`${API_BASE_URL}/audit/fiches-de-test-attachments/${ficheTest.id}?type=business-document&_t=${timestamp}`, {
          withCredentials: true,
          headers: { "Cache-Control": "no-cache", "Pragma": "no-cache" },
        }),
        axios.get(`${API_BASE_URL}/audit/fiches-de-test-attachments/${ficheTest.id}?type=external-reference&_t=${timestamp}`, {
          withCredentials: true,
          headers: { "Cache-Control": "no-cache", "Pragma": "no-cache" },
        }),
      ]);

      if (businessDocsResponse.data.success) {
        setBusinessDocuments(businessDocsResponse.data.data);
      }
      if (externalRefsResponse.data.success) {
        setExternalReferences(externalRefsResponse.data.data);
      }
    } catch (error) {
      console.error("Error fetching attachments:", error);
      if (error.response?.data?.message?.includes("relation") && error.response?.data?.message?.includes("does not exist")) {
        console.log("FicheDeTestAttachment table does not exist yet. This is normal if no attachments have been uploaded.");
      } else {
        toast.error("Failed to load attachments", {
          description: "Please try refreshing the page",
          duration: 5000,
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [ficheTest?.id]);

  useEffect(() => {
    if (ficheTest?.id) {
      fetchAttachments();
    }
  }, [ficheTest?.id, fetchAttachments]);

  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    const allowedExtensions = [
      ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
      ".txt", ".csv", ".rtf", ".odt", ".ods", ".odp",
      ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
      ".zip", ".rar", ".7z", ".tar", ".gz",
    ];

    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map((file) => file.name).join(", ");
      toast.error(`Files too large: ${fileNames}`, {
        description: `The following files exceed the 50MB limit: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter((file) => {
      const extension = "." + file.name.split(".").pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map((file) => file.name).join(", ");
      toast.error(`Unsupported file types: ${fileNames}`, {
        description: `The following files have unsupported formats: ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    try {
      setIsLoading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append("type", "business-document");
      formData.append("ficheDeTestID", ficheTest.id);
      files.forEach((file) => formData.append("files", file));

      const response = await axios.post(`${API_BASE_URL}/audit/fiches-de-test-attachments/upload`, formData, {
        withCredentials: true,
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
      });

      if (response.data.success) {
        toast.success(`Uploaded ${files.length} document${files.length !== 1 ? "s" : ""} successfully`, {
          description: `${files.length} document${files.length !== 1 ? "s" : ""} uploaded successfully`,
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error("Error uploading business documents:", error);
      if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
        toast.error("Upload timed out", {
          description: "Try with smaller files or a better connection",
          duration: 5000,
        });
      } else if (error.response?.status === 413) {
        toast.error("File too large", {
          description: "The file exceeds the maximum size limit of 50MB",
          duration: 5000,
        });
      } else if (error.response?.data?.message?.includes("file type")) {
        toast.error(`Unsupported file type: ${error.response.data.message}`, {
          description: error.response.data.message,
          duration: 5000,
        });
      } else {
        const errorMsg = error.response?.data?.message || "Failed to upload documents";
        toast.error(errorMsg, { description: errorMsg, duration: 5000 });
      }
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = "";
    }
  };

  const handleAddExternalReference = async () => {
    if (!newReference.url || !newReference.url.trim()) {
      toast.error("URL is required");
      return;
    }

    try {
      setIsLoading(true);
      const formattedUrl = newReference.url.startsWith("http://") || newReference.url.startsWith("https://")
        ? newReference.url
        : `https://${newReference.url}`;

      const response = await axios.post(
        `${API_BASE_URL}/audit/fiches-de-test-attachments/reference`,
        { ficheDeTestID: ficheTest.id, url: formattedUrl, description: newReference.description },
        { withCredentials: true, headers: { "Content-Type": "application/json" } }
      );

      if (response.data.success) {
        toast.success("Reference added successfully");
        setIsReferenceModalOpen(false);
        setNewReference({ url: "", description: "" });
        fetchAttachments();
      }
    } catch (error) {
      console.error("Error adding external reference:", error);
      toast.error("Failed to add reference", {
        description: error.response?.data?.message || "An error occurred",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadAttachment = async (id, fileName) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/audit/fiches-de-test-attachments/download/${id}`, {
        withCredentials: true,
        responseType: "blob",
      });

      const contentDisposition = response.headers["content-disposition"];
      let actualFileName = fileName;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-8''|)([a-zA-Z0-9%\-._ ]+)['"]?/i);
        if (filenameMatch && filenameMatch[1]) {
          try {
            actualFileName = decodeURIComponent(filenameMatch[1]);
          } catch (_e) {
            console.warn("Could not decode filename:", filenameMatch[1]);
            actualFileName = filenameMatch[1];
          }
        }
      }

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", actualFileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading attachment:", error);
      toast.error("Download failed", {
        description: "Unable to download the file. Please try again later.",
        duration: 5000,
      });
    }
  };

  const deleteBusinessDocument = async (id) => {
    if (!window.confirm("Are you sure you want to delete this document?")) return;
    try {
      const response = await axios.delete(`${API_BASE_URL}/audit/fiches-de-test-attachments/${id}`, { withCredentials: true });
      if (response.data.success) {
        toast.success("Document deleted", {
          description: "The document was successfully deleted",
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error("Error deleting business document:", error);
      toast.error("Delete failed", {
        description: "Unable to delete the document. Please try again later.",
        duration: 5000,
      });
    }
  };

  const deleteExternalReference = async (id) => {
    if (!window.confirm("Are you sure you want to delete this reference?")) return;
    try {
      const response = await axios.delete(`${API_BASE_URL}/audit/fiches-de-test-attachments/${id}`, { withCredentials: true });
      if (response.data.success) {
        toast.success("Reference deleted", {
          description: "The reference was successfully deleted",
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error("Error deleting external reference:", error);
      toast.error("Delete failed", {
        description: "Unable to delete the reference. Please try again later.",
        duration: 5000,
      });
    }
  };

  const openExternalReference = (url) => {
    const formattedUrl = url.startsWith("http://") || url.startsWith("https://") ? url : `https://${url}`;
    window.open(formattedUrl, "_blank", "noopener,noreferrer");
  };

  const formatFileSize = (bytes) => {
    if (bytes == null || isNaN(bytes)) return "N/A";
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Invalid Date";
    }
  };

  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    const event = { target: { files, value: "" } };
    handleBusinessDocumentUpload(event);
  };

  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div>
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            Business Documents
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            External References
          </TabsTrigger>
        </TabsList>

        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Business Documents</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Uploading... {uploadProgress > 0 ? `${uploadProgress}%` : ""}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Upload Document
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingBusiness ? "border-blue-500 bg-blue-50" : "border-gray-300"
              }`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">
                {isDraggingBusiness ? "Drop files here" : "No business documents uploaded yet"}
              </p>
              <p className="text-sm text-gray-400">Drag & drop files here or click the upload button</p>
              <p className="text-xs text-gray-400 mt-2">Max file size: 50MB. Allowed file types: PDF, Office documents, images, archives.</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Size</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-blue-500" />
                          {doc.name}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatFileSize(doc.size)}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(doc.uploadDate)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => downloadAttachment(doc.id, doc.name)}
                            title="Download"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteBusinessDocument(doc.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">External References</h3>
            <Button
              type="button"
              variant="outline"
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={() => setIsReferenceModalOpen(true)}
            >
              <Plus className="h-4 w-4" />
              Add Reference
            </Button>
          </div>

          {externalReferences.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed rounded-lg border-gray-300">
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">No external references added yet</p>
              <p className="text-sm text-gray-400">Click the 'Add Reference' button to add a link</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Description</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref) => (
                    <tr key={ref.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <Link className="h-4 w-4 mr-2 text-green-500" />
                          <span className="text-blue-600 hover:underline cursor-pointer" onClick={() => openExternalReference(ref.url)}>
                            {ref.name || new URL(ref.url).hostname}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{ref.description}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(ref.createdAt)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => openExternalReference(ref.url)}
                            title="Open Link"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteExternalReference(ref.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add External Reference</DialogTitle>
            <DialogDescription>
              Add a link to an external resource related to this fiche de test.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">
                URL <span className="text-red-500">*</span>
              </Label>
              <Input
                id="url"
                name="url"
                placeholder="https://www.example.com"
                value={newReference.url}
                onChange={handleReferenceInputChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Briefly describe this reference"
                value={newReference.description}
                onChange={handleReferenceInputChange}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReferenceModalOpen(false)} type="button">
              Cancel
            </Button>
            <Button onClick={handleAddExternalReference} disabled={isLoading} type="button">
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Add Reference
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}