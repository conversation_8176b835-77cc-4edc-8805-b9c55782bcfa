import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  ClipboardList, 
  Save, 
  FileSymlink, 
  ChevronDown, 
  ChevronUp, 
  FileText, 
  Calendar, 
  Users, 
  Clock, 
  BarChart, 
  Target, 
  CheckSquare, 
  Briefcase,
  AlertTriangle,
  CheckCircle,
  CircleAlert
} from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { Checkbox } from "@/components/ui/checkbox";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Add Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error in component:', error);
    console.error('Error info:', errorInfo);
    toast.error('Une erreur est survenue dans le composant');
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-500 rounded bg-red-50">
          <h2 className="text-red-700 font-bold">Une erreur est survenue</h2>
          <pre className="text-sm text-red-600 mt-2">
            {this.state.error?.toString()}
          </pre>
          <Button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2"
          >
            Réessayer
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

function CaracteristiquesTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  const [users, setUsers] = useState([]);

  // Section states
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isMotivationsOpen, setIsMotivationsOpen] = useState(true);
  const [isJalonsOpen, setIsJalonsOpen] = useState(true);
  const [isResponsabilitesOpen, setIsResponsabilitesOpen] = useState(true);
  const [isCompetencesOpen, setIsCompetencesOpen] = useState(true);
  const [isConclusionOpen, setIsConclusionOpen] = useState(true);
  
  // Initialize characteristics with safe defaults
  const [characteristics, setCharacteristics] = useState({
    title: '',
    code: '',
    category: '',
    status: 'En cours',
    auditPlan: '',
    includedInInitialPlan: false,
    leadAuditor: '',
    mainAudited: '',
    objectives: '',
    pointsForts: '',
    pointsFaibles: '',
    evaluationLevel: 'Bon niveau',
    progression: 0,
    evaluation: 'Bon niveau',
    recommendations: 0
  });

  // Simple user fetch with error handling
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await axios.get(`${getApiBaseUrl()}/users`);
        if (response.data.success && Array.isArray(response.data.data)) {
          setUsers(response.data.data);
        } else {
          setUsers([]);
          toast.error('Données des utilisateurs non valides');
        }
      } catch {
        setUsers([]);
        toast.error('Erreur lors du chargement des utilisateurs');
      }
    };
    fetchUsers();
  }, []);

  // Update characteristics when mission data changes
  useEffect(() => {
    if (missionAudit) {
      setCharacteristics({
        title: missionAudit.name || '',
        code: missionAudit.code || '',
        category: missionAudit.categorie || '',
        status: missionAudit.etat || 'En cours',
        auditPlan: missionAudit.auditPlan?.name || '',
        includedInInitialPlan: missionAudit.planifieInitialement || false,
        leadAuditor: missionAudit.missionChief?.id?.toString() || '',
        mainAudited: missionAudit.principalAudite || '',
        objectives: missionAudit.objectif || '',
        pointsForts: missionAudit.pointfort || '',
        pointsFaibles: missionAudit.pointfaible || '',
        evaluationLevel: missionAudit.evaluation || 'Bon niveau',
        progression: parseInt(missionAudit.avancement) || 0,
        evaluation: missionAudit.evaluation || 'Bon niveau',
        recommendations: 0
      });
    }
  }, [missionAudit, lastUpdate]);

  // Function to refresh mission data
  const refreshMissionData = async () => {
    try {
      const response = await axios.get(`${getApiBaseUrl()}/audit-missions/${missionAudit.id}`, {
        params: {
          include: ['missionChief', 'auditPlan'] // Include related data
        }
      });
      if (response.data.success) {
        // Update local state with fresh data
        setCharacteristics({
          title: response.data.data.name || '',
          code: response.data.data.code || '',
          category: response.data.data.categorie || '',
          status: response.data.data.etat || 'En cours',
          auditPlan: response.data.data.auditPlan?.name || '',
          includedInInitialPlan: response.data.data.planifieInitialement || false,
          leadAuditor: response.data.data.missionChief?.id?.toString() || '',
          mainAudited: response.data.data.principalAudite || '',
          objectives: response.data.data.objectif || '',
          pointsForts: response.data.data.pointfort || '',
          pointsFaibles: response.data.data.pointfaible || '',
          evaluationLevel: response.data.data.evaluation || 'Bon niveau',
          progression: parseInt(response.data.data.avancement) || 0,
          evaluation: response.data.data.evaluation || 'Bon niveau',
          recommendations: 0
        });
        setLastUpdate(Date.now());
      }
    } catch (error) {
      console.error('Error refreshing mission data:', error);
    }
  };

  // Refresh data when tab becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        refreshMissionData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [missionAudit?.id]);

  // Initial data load and refresh on missionAudit change
  useEffect(() => {
    if (missionAudit?.id) {
      refreshMissionData();
    }
  }, [missionAudit?.id]);

  // If no mission data is available yet, show loading
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des caractéristiques de la mission...</p>
      </div>
    );
  }

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const missionData = {
        name: characteristics.title,
        code: characteristics.code,
        categorie: characteristics.category,
        etat: characteristics.status,
        chefmission: characteristics.leadAuditor === '' ? null : characteristics.leadAuditor,
        principalAudite: characteristics.mainAudited,
        objectif: characteristics.objectives,
        pointfort: characteristics.pointsForts,
        pointfaible: characteristics.pointsFaibles,
        evaluation: characteristics.evaluationLevel,
        avancement: characteristics.progression.toString(),
        planifieInitialement: characteristics.includedInInitialPlan
      };

      const saveResponse = await axios.put(
        `${getApiBaseUrl()}/audit-missions/${missionAudit.id}`,
        missionData
      );

      if (saveResponse.data.success) {
        toast.success("Caractéristiques sauvegardées avec succès");
        
        // Update parent component data
        if (contextData.updateMissionData) {
          await contextData.updateMissionData();
        }
        
        // Also update local state
        await refreshMissionData();
      } else {
        toast.error(saveResponse.data.message || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      console.error('Error saving mission characteristics:', error);
      toast.error(error.response?.data?.message || "Erreur lors de la sauvegarde");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCharacteristics(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setCharacteristics(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name, checked) => {
    setCharacteristics(prev => ({ ...prev, [name]: checked }));
  };

  // Helper functions for color determination
  const _getProgressionColor = (value) => {
    if (value >= 80) return "bg-green-500";
    if (value >= 60) return "bg-blue-500";
    if (value >= 40) return "bg-yellow-400";
    if (value >= 20) return "bg-orange-500";
    return "bg-red-500";
  };

  const _getEvaluationColor = (level) => {
    switch (level) {
      case "Bon niveau": return "bg-green-500";
      case "Peut être amélioré": return "bg-yellow-500";
      case "Amélioration nécessaire": return "bg-orange-500";
      case "A risque": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const _getRecommendationsColor = (count) => {
    if (count === 0) return "bg-green-500";
    if (count <= 5) return "bg-blue-500";
    if (count <= 10) return "bg-yellow-400";
    if (count <= 15) return "bg-orange-500";
    return "bg-red-500";
  };

  // Helper function to get selected user's username for display
  const getSelectedUserName = () => {
    if (!characteristics.leadAuditor) return '';
    const selectedUser = users.find(user => user.id.toString() === characteristics.leadAuditor);
    return selectedUser ? selectedUser.username : '';
  };

  // FIXED: Update the chef de mission select component with better error handling
  const renderChefDeMission = () => (
    <div className="col-span-2 space-y-2">
      <Label htmlFor="leadAuditor">Chef de mission</Label>
      <Select
        name="leadAuditor"
        value={characteristics.leadAuditor}
        onValueChange={(value) => handleSelectChange("leadAuditor", value)}
      >
        <SelectTrigger id="leadAuditor">
          <SelectValue placeholder="Sélectionner un chef de mission">
            {getSelectedUserName()}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {users.map(user => (
            <SelectItem key={user.id} value={user.id.toString()}>
              {user.username}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
  // Wrap the main component content in ErrorBoundary
  return (
    <ErrorBoundary>
      <div className="space-y-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
            <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
            Caractéristiques de la Mission
          </h2>
          <Button 
            onClick={handleSave} 
            className="bg-[#F62D51] hover:bg-[#F62D51]/90"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isLoading ? "Sauvegarde..." : "Sauvegarder"}
          </Button>
        </div>

        {/* Section Caractéristiques */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
            onClick={() => setIsCaracteristiquesOpen(!isCaracteristiquesOpen)}
          >
            <div className="flex items-center gap-2">
              {isCaracteristiquesOpen ? (
                <ChevronUp className="h-5 w-5 text-blue-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-blue-600" />
              )}
              <FileText className="h-5 w-5 text-blue-600 mr-1" />
              <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
            </div>
          </button>

          {isCaracteristiquesOpen && (
            <div className="p-5 bg-white">
              <div className="grid grid-cols-4 gap-4">
                {/* First row */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="title">Nom *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={characteristics.title}
                    onChange={handleInputChange}
                    placeholder="Nom de la mission d'audit"
                    className="w-full"
                  />
                </div>
                  
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="code">Code *</Label>
                  <Input 
                    id="code" 
                    name="code"
                    value={characteristics.code}
                    onChange={handleInputChange}
                    placeholder="Code"
                    className="w-full"
                  />
                </div>
                  
                {/* Second row */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="category">Catégorie</Label>
                  <Select 
                    name="category"
                    value={characteristics.category} 
                    onValueChange={(value) => handleSelectChange("category", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="category" className="w-full">
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Conformité">Conformité</SelectItem>
                      <SelectItem value="Performance">Efficacité</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="status">État</Label>
                  <Select 
                    name="status"
                    value={characteristics.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="status" className="w-full">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Planifiée">Planifiée</SelectItem>
                      <SelectItem value="En cours">En cours</SelectItem>
                      <SelectItem value="Terminée">Terminée</SelectItem>
                      <SelectItem value="Suspendue">Suspendue</SelectItem>
                      <SelectItem value="Annulée">Annulée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  
                {/* Third row - FIXED: Plan d'audit is now read-only */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="auditPlan">Plan d'audit</Label>
                  <Input 
                    id="auditPlan" 
                    name="auditPlan"
                    value={characteristics.auditPlan}
                    readOnly
                    placeholder="Plan d'audit (lecture seule)"
                    className="w-full bg-gray-50 cursor-not-allowed"
                  />
                </div>

                <div className="col-span-1 flex items-end space-y-2">
                  <div className="flex items-center space-x-2 h-10 pt-4 w-full">
                    <Checkbox 
                      id="includedInInitialPlan" 
                      checked={characteristics.includedInInitialPlan}
                      onCheckedChange={(checked) => handleCheckboxChange("includedInInitialPlan", checked)}
                    />
                    <Label htmlFor="includedInInitialPlan">Inclus dans le plan initial</Label>
                  </div>
                </div>
              
                {/* Fourth row - FIXED: Chef de mission with better error handling */}
                {renderChefDeMission()}
                
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="mainAudited">Principal audité</Label>
                  <Input 
                    id="mainAudited" 
                    name="mainAudited"
                    value={characteristics.mainAudited}
                    onChange={handleInputChange}
                    placeholder="Principal audité"
                    className="w-full"
                  />
                </div>
                
                {/* Fifth row */}
                <div className="col-span-4 space-y-2">
                  <Label htmlFor="objectives">Objectif *</Label>
                  <Textarea 
                    id="objectives" 
                    name="objectives"
                    value={characteristics.objectives}
                    onChange={handleInputChange}
                    placeholder="Objectifs principaux de la mission d'audit"
                    rows={3}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Section Motivations et Charge */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
            onClick={() => setIsMotivationsOpen(!isMotivationsOpen)}
          >
            <div className="flex items-center gap-2">
              {isMotivationsOpen ? (
                <ChevronUp className="h-5 w-5 text-green-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-green-600" />
              )}
              <Target className="h-5 w-5 text-green-600 mr-1" />
              <span className="text-lg font-medium text-green-800">Motivations et Charge</span>
            </div>
          </button>

          {isMotivationsOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Jalons */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-t-lg"
            onClick={() => setIsJalonsOpen(!isJalonsOpen)}
          >
            <div className="flex items-center gap-2">
              {isJalonsOpen ? (
                <ChevronUp className="h-5 w-5 text-amber-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-amber-600" />
              )}
              <Calendar className="h-5 w-5 text-amber-600 mr-1" />
              <span className="text-lg font-medium text-amber-800">Jalons</span>
            </div>
          </button>

          {isJalonsOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Responsabilités */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg"
            onClick={() => setIsResponsabilitesOpen(!isResponsabilitesOpen)}
          >
            <div className="flex items-center gap-2">
              {isResponsabilitesOpen ? (
                <ChevronUp className="h-5 w-5 text-purple-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-purple-600" />
              )}
              <Users className="h-5 w-5 text-purple-600 mr-1" />
              <span className="text-lg font-medium text-purple-800">Responsabilités</span>
            </div>
          </button>

          {isResponsabilitesOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Compétences */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg"
            onClick={() => setIsCompetencesOpen(!isCompetencesOpen)}
          >
            <div className="flex items-center gap-2">
              {isCompetencesOpen ? (
                <ChevronUp className="h-5 w-5 text-teal-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-teal-600" />
              )}
              <Briefcase className="h-5 w-5 text-teal-600 mr-1" />
              <span className="text-lg font-medium text-teal-800">Compétences</span>
            </div>
          </button>

          {isCompetencesOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Conclusion */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-t-lg"
            onClick={() => setIsConclusionOpen(!isConclusionOpen)}
          >
            <div className="flex items-center gap-2">
              {isConclusionOpen ? (
                <ChevronUp className="h-5 w-5 text-red-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-red-600" />
              )}
              <CheckSquare className="h-5 w-5 text-red-600 mr-1" />
              <span className="text-lg font-medium text-red-800">Conclusion</span>
            </div>
          </button>

          {isConclusionOpen && (
            <div className="p-5 bg-white">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="pointsForts">Points forts clés</Label>
                  <Textarea 
                    id="pointsForts" 
                    name="pointsForts"
                    value={characteristics.pointsForts}
                    onChange={handleInputChange}
                    placeholder="Points forts identifiés lors de l'audit"
                    rows={4}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="pointsFaibles">Points faibles clés</Label>
                  <Textarea 
                    id="pointsFaibles" 
                    name="pointsFaibles"
                    value={characteristics.pointsFaibles}
                    onChange={handleInputChange}
                    placeholder="Points faibles identifiés lors de l'audit"
                    rows={4}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-3">
                  <Label>Évaluation globale</Label>
                  <Select 
                    name="evaluationLevel"
                    value={characteristics.evaluationLevel} 
                    onValueChange={(value) => handleSelectChange("evaluationLevel", value)}
                    className="w-full"
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Sélectionner une évaluation" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Bon niveau" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-green-500 mr-2 inline-block"></span>
                          Bon niveau
                        </div>
                      </SelectItem>
                      <SelectItem value="Peut être amélioré" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-yellow-500 mr-2 inline-block"></span>
                          Peut être amélioré
                        </div>
                      </SelectItem>
                      <SelectItem value="Amélioration nécessaire" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-orange-500 mr-2 inline-block"></span>
                          Amélioration nécessaire
                        </div>
                      </SelectItem>
                      <SelectItem value="A risque" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-red-500 mr-2 inline-block"></span>
                          A risque
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default CaracteristiquesTab;
