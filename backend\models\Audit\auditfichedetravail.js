'use strict';

module.exports = (sequelize, DataTypes) => {
  const FicheDeTravail = sequelize.define('FicheDeTravail', {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    tailleEchantillon: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    questionnaire: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tacheDetaillees: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    commentaire: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    auditActivityID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'AuditActivities',
        key: 'id'
      }
    },
    auditMissionID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    }
  }, {
    tableName: 'FicheDeTravail',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditActivityID'] // For foreign key lookups
      },
      {
        fields: ['auditMissionID'] // For foreign key lookups
      },
      {
        fields: ['name'] // For name searches
      },
      {
        fields: ['createdAt'] // For date-based queries
      },
      {
        fields: ['updatedAt'] // For date-based queries
      }
    ]
  });

  FicheDeTravail.associate = function(models) {
    FicheDeTravail.belongsTo(models.AuditActivity, {
      foreignKey: 'auditActivityID',
      as: 'auditActivity'
    });
    FicheDeTravail.belongsTo(models.AuditMission, {
      foreignKey: 'auditMissionID',
      as: 'auditMission'
    });
    FicheDeTravail.hasMany(models.Question, {
      foreignKey: 'ficheDeTravailID',
      as: 'questions'
    });
  };

  return FicheDeTravail;
};