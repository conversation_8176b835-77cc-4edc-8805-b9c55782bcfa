import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Paperclip, ChevronUp, ChevronDown, Save, Loader2, FileText, Plus, Trash2, Upload, ArrowUp, ArrowDown } from "lucide-react";
import FicheAttachmentsSection from "@/components/common/FicheAttachmentsSection";
import { useCustomOutletContext } from "../../edit-fiches-travail";
import { updateFicheDeTravail, deleteQuestion, reorderQuestions } from "@/services/fiche-de-travail-service";
import { toast } from "sonner";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { Combobox } from "@/components/ui/combobox";

function ********************************() {
  const context = useCustomOutletContext();
  const fiche = context?.fiche;

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isQuestionnaireOpen, setIsQuestionnaireOpen] = useState(true);
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(true);

  // Loading states
  const [isSaving, setIsSaving] = useState(false);

  // Form state for fiche
  const [formData, setFormData] = useState({
    name: "",
    auditMissionID: "",
    auditActivityID: "",
    tailleEchantillon: "",
    tacheDetail: "",
    commentaire: ""
  });

  // Quiz state
  const [questions, setQuestions] = useState([]);
  const [newQuestion, setNewQuestion] = useState({
    question_text: "",
    input_type: "text",
    options: []
  });
  const [option, setOption] = useState("");

  // Track which question is being deleted
  const [deletingQuestionId, setDeletingQuestionId] = useState(null);

  // Track which question is being reordered
  const [reorderingQuestionId, setReorderingQuestionId] = useState(null);

  const questionnaireRef = useRef(null);

  // Initialize form data when fiche is loaded
  useEffect(() => {
    if (fiche) {
      setFormData({
        name: fiche.name || "",
        auditMissionID: fiche.auditMissionID || "",
        auditActivityID: fiche.auditActivityID || '',
        tailleEchantillon: fiche.tailleEchantillon || 'N/A',
        tacheDetail: fiche.tacheDetail || '',
        commentaire: fiche.commentaire || ''
      });
      // Initialize questions from fiche if available
      setQuestions(fiche.questions || []);
    }
  }, [fiche]);

  // Handlers for fiche form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      return;
    }

    setIsSaving(true);
    try {
      // Include questions in the update payload
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions });
      if (response && response.success) {
        toast.success("Fiche de travail mise à jour avec succès");
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la mise à jour");
      }
    } catch (error) {
      console.error('Error updating fiche:', error);
      toast.error(error.message || "Erreur lors de la mise à jour de la fiche de travail");
    } finally {
      setIsSaving(false);
    }
  };

  // Handlers for quiz form
  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setNewQuestion((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddOption = () => {
    if (option.trim()) {
      setNewQuestion((prev) => ({
        ...prev,
        options: [...prev.options, option.trim()]
      }));
      setOption("");
    }
  };

  const handleRemoveOption = (index) => {
    setNewQuestion((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const handleAddQuestion = async () => {
    if (!newQuestion.question_text.trim()) {
      toast.error("Veuillez entrer une question");
      return;
    }
    if (["radio", "select", "multi-select"].includes(newQuestion.input_type) && newQuestion.options.length === 0) {
      toast.error("Veuillez ajouter au moins une option pour ce type de question");
      return;
    }

    // Add the new question to the local state
    const updatedQuestions = [
      ...questions,
      {
        // No id, let backend assign
        question_text: newQuestion.question_text,
        input_type: newQuestion.input_type,
        options: ["radio", "select", "multi-select"].includes(newQuestion.input_type) ? [...newQuestion.options] : null
      }
    ];

    // Save immediately to backend
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      return;
    }
    setIsSaving(true);
    try {
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions: updatedQuestions });
      if (response && response.success) {
        toast.success("Question ajoutée et sauvegardée avec succès");
        // Update local state with backend's latest questions (with IDs)
        if (response.data?.questions) {
          setQuestions(response.data.questions);
        } else {
          setQuestions(updatedQuestions);
        }
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la sauvegarde de la question");
      }
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error(error.message || "Erreur lors de la sauvegarde de la question");
    } finally {
      setIsSaving(false);
    }

    // Reset form
    setNewQuestion({
      question_text: "",
      input_type: "text",
      options: []
    });
    setOption("");
  };

  const handleDeleteQuestion = async (id) => {
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      return;
    }
    setDeletingQuestionId(id);
    setIsSaving(true);
    try {
      // Delete question from backend
      await deleteQuestion(id);
      // Fetch updated questions from backend
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions: questions.filter((q) => q.id !== id) });
      if (response && response.success) {
    toast.success("Question supprimée avec succès");
        if (response.data?.questions) {
          setQuestions(response.data.questions);
        } else {
          setQuestions(questions.filter((q) => q.id !== id));
        }
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la suppression de la question");
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error(error.message || "Erreur lors de la suppression de la question");
    } finally {
      setIsSaving(false);
      setDeletingQuestionId(null);
    }
  };

  const handleMoveUp = async (index) => {
    if (index === 0) return;
    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
      [newQuestions[index - 1], newQuestions[index]] = [newQuestions[index], newQuestions[index - 1]];
    setQuestions(newQuestions);
    // Call backend to reorder
    try {
      await reorderQuestions(fiche.id, newQuestions.map(q => q.id));
    } finally {
      setReorderingQuestionId(null);
    }
  };

  const handleMoveDown = async (index) => {
    if (index === questions.length - 1) return;
    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
      [newQuestions[index], newQuestions[index + 1]] = [newQuestions[index + 1], newQuestions[index]];
    setQuestions(newQuestions);
    // Call backend to reorder
    try {
      await reorderQuestions(fiche.id, newQuestions.map(q => q.id));
    } finally {
      setReorderingQuestionId(null);
    }
  };

  if (!fiche) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-500"></div>
        <span className="ml-4 text-red-700">Chargement de la fiche de travail...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header with title and save button */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <FileText className="h-6 w-6 mr-3 text-[#F62D51]" />
          Caractéristiques de la fiche de travail
        </h2>
        <Button 
          onClick={handleSave} 
          className="bg-[#F62D51] hover:bg-[#F62D51]/90"
          disabled={isSaving}
        >
          {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
          {isSaving ? "Validation..." : "Valider"}
        </Button>
      </div>

      {/* Section 1: Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-6">
            {/* Row 1: Nom */}
            <div className="w-full space-y-2">
              <Label htmlFor="name">Nom *</Label>
              <Input 
                id="name" 
                name="name" 
                value={formData.name} 
                onChange={handleInputChange} 
                placeholder="Nom de la fiche de travail" 
                className="w-full" 
                required 
              />
            </div>
            
            {/* Row 2: Mission d'audit, Activité d'audit */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="auditMissionID">Mission d'audit</Label>
                <Input 
                  id="auditMissionID" 
                  name="auditMissionID" 
                  value={fiche?.auditMission?.name || 'N/A'} 
                  className="w-full bg-gray-50" 
                  readOnly 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="auditActivityID">Activité d'audit</Label>
                <Input 
                  id="auditActivityID" 
                  name="auditActivityID" 
                  value={fiche?.auditActivity?.name || 'N/A'} 
                  className="w-full bg-gray-50" 
                  readOnly 
                />
              </div>
            </div>
            
            {/* Row 3: Taille d'échantillon, Editer le modèle de questionnaire */}
            <div className="grid grid-cols-4 gap-4">
              <div className="space-y-2 col-span-1">
                <Label htmlFor="tailleEchantillon">Taille d'échantillon</Label>
                <Input 
                  id="tailleEchantillon" 
                  name="tailleEchantillon" 
                  type="text" 
                  value={formData.tailleEchantillon} 
                  onChange={handleInputChange} 
                  placeholder="Taille" 
                  className="w-full" 
                />
              </div>
              <div className="flex items-end col-span-3">
                <Button
                  className="w-full border border-[#d7d7d7] shadow-md"
                  variant="outline"
                  onClick={() => {
                    if (questionnaireRef.current) {
                      const y = questionnaireRef.current.getBoundingClientRect().top + window.pageYOffset - 70;
                      window.scrollTo({ top: y, behavior: 'smooth' });
                    }
                  }}
                >
                  Éditer le modèle de questionnaire
                </Button>
              </div>
            </div>
            
            {/* Row 4: Tâche détaillée */}
            <div className="w-full space-y-2">
              <Label htmlFor="tacheDetail">Tâche détaillée</Label>
              <Textarea 
                id="tacheDetail" 
                name="tacheDetail" 
                value={formData.tacheDetail} 
                onChange={handleInputChange} 
                placeholder="Tâche détaillée" 
                rows={3} 
                className="w-full" 
              />
            </div>
            
            {/* Row 5: Commentaires */}
            <div className="w-full space-y-2">
              <Label htmlFor="commentaire">Commentaires</Label>
              <Textarea 
                id="commentaire" 
                name="commentaire" 
                value={formData.commentaire} 
                onChange={handleInputChange} 
                placeholder="Commentaires" 
                rows={3} 
                className="w-full" 
              />
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Modèle de Questionnaire */}
      <div ref={questionnaireRef} className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
          onClick={() => setIsQuestionnaireOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isQuestionnaireOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <span className="text-lg font-medium text-green-800">Modèle de Questionnaire</span>
          </div>
        </button>
        {isQuestionnaireOpen && (
          <div className="p-5 bg-white space-y-6">
            {/* Form to add new question */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="question_text">Question *</Label>
                <Input
                  id="question_text"
                  name="question_text"
                  value={newQuestion.question_text}
                  onChange={handleQuestionInputChange}
                  placeholder="Entrez la question"
                  className="w-full"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="input_type">Type d'entrée</Label>
                <Select
                  value={newQuestion.input_type}
                  onValueChange={(value) => setNewQuestion((prev) => ({ ...prev, input_type: value }))}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionnez un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Texte</SelectItem>
                    <SelectItem value="number">Nombre</SelectItem>
                    <SelectItem value="radio">Radio</SelectItem>
                    <SelectItem value="select">Sélection</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="multi-select">Multi-sélection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {["radio", "select", "multi-select"].includes(newQuestion.input_type) && (
                <div className="space-y-2">
                  <Label>Options</Label>
                  <div className="flex gap-2">
                    <Input
                      value={option}
                      onChange={(e) => setOption(e.target.value)}
                      placeholder="Entrez une option"
                      className="w-full"
                    />
                    <Button
                      onClick={handleAddOption}
                      className="bg-white border border-[#F62D51] text-[#F62D51] hover:bg-[#f3f3f3] hover:text-[#F62D51]"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter
                    </Button>
                  </div>
                  <div className="mt-2 space-y-1">
                    {newQuestion.options.map((opt, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-100 rounded">
                        <span>{opt}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveOption(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              <Button
                onClick={handleAddQuestion}
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter ce modèle
              </Button>
            </div>

            {/* List of added questions */}
            {questions.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Questions ajoutées</h3>
                <div className="space-y-4">
                  {questions.map((q, index) => (
                    <div key={q.id} className={`p-4 bg-gray-50 rounded-lg border flex justify-between items-start ${deletingQuestionId === q.id ? 'opacity-50 pointer-events-none' : ''}`}>
                      <div>
                        <p className="font-semibold">{q.question_text}</p>
                        <p className="text-sm text-gray-600">Type: {q.input_type}</p>
                        {q.options && (
                          <ul className="list-disc pl-5 mt-2">
                            {q.options.map((opt, idx) => (
                              <li key={idx} className="text-sm">{opt}</li>
                            ))}
                          </ul>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveUp(index)}
                          disabled={index === 0 || reorderingQuestionId === q.id || isSaving}
                          className="text-green-500 hover:text-green-700 disabled:text-gray-400"
                          title="Déplacer vers le haut"
                        >
                          {reorderingQuestionId === q.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowUp className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveDown(index)}
                          disabled={index === questions.length - 1 || reorderingQuestionId === q.id || isSaving}
                          className="text-green-500 hover:text-green-700 disabled:text-gray-400"
                          title="Déplacer vers le bas"
                        >
                          {reorderingQuestionId === q.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowDown className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteQuestion(q.id)}
                          className="text-red-500 hover:text-red-700"
                          title="Supprimer la question"
                          disabled={deletingQuestionId === q.id || isSaving}
                        >
                          {deletingQuestionId === q.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                          <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Section 3: Pièces jointes */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg"
          onClick={() => setIsAttachmentsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            <Paperclip className="h-5 w-5 text-gray-600 mr-1" />
            <span className="text-lg font-medium text-gray-800">Pièces jointes</span>
          </div>
        </button>
        {isAttachmentsOpen && (
          <div className="p-5 bg-white">
            <div className="p-4 border border-dashed border-gray-300 rounded bg-gray-50 text-center">
              <p className="text-sm text-gray-500">Pièces jointes (à implémenter)</p>
              <Button variant="outline" className="mt-2" disabled>Upload (à implémenter)</Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ********************************;