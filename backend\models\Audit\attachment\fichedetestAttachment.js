'use strict';

module.exports = (sequelize, DataTypes) => {
  const FicheDeTestAttachment = sequelize.define('FicheDeTestAttachment', {
    attachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    ficheDeTestID: {
      type: DataTypes.STRING,
      allowNull: false
    }
  }, {
    tableName: 'FicheDeTestAttachments',
    timestamps: true
  });

  // No associations defined here to avoid foreign key issues

  return FicheDeTestAttachment;
};


