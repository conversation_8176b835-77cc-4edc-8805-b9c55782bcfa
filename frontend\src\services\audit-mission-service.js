import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';

// Get all audit missions
export const getAllAuditMissions = async (signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl('audit-missions'),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get audit mission by ID
export const getAuditMissionById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-missions/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Get audit missions by plan ID
export const getAuditMissionsByPlanId = async (planId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-missions/plan/${planId}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Create new audit mission
export const createAuditMission = async (missionData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit-missions'),
      missionData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Update audit mission
export const updateAuditMission = async (id, missionData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-missions/${id}`),
      missionData,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

// Delete audit mission
export const deleteAuditMission = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit-missions/${id}`),
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 