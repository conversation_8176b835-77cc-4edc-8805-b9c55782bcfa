const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const db = require('../../../../backend/models'); // Adjust path as per your project structure
const FicheDeTestAttachment = db.FicheDeTestAttachment;
const FicheDeTest = db.FicheDeTest; // Assuming you have a FicheDeTest model

// Add this for debugging
console.log('FicheDeTest model:', !!require('../../../models').FicheDeTest);

// Define upload directories
const UPLOADS_BASE_DIR = path.join(__dirname, '..', '..', '..', '..', 'uploads'); // Navigate up to the project root
const ficheDeTestDocsDir = path.join(UPLOADS_BASE_DIR, 'audit', 'ficheDeTest', 'documents');
const ficheDeTestRefsDir = path.join(UPLOADS_BASE_DIR, 'audit', 'ficheDeTest', 'references');

// Ensure upload directories exist
fs.existsSync(ficheDeTestDocsDir) || fs.mkdirSync(ficheDeTestDocsDir, { recursive: true });
fs.existsSync(ficheDeTestRefsDir) || fs.mkdirSync(ficheDeTestRefsDir, { recursive: true });

// Add these logs
console.log('Uploads directory exists:', fs.existsSync(UPLOADS_BASE_DIR));
console.log('Fiche de test docs directory exists:', fs.existsSync(ficheDeTestDocsDir));
console.log('Fiche de test refs directory exists:', fs.existsSync(ficheDeTestRefsDir));

// Upload file attachment
const uploadFile = async (req, res) => {
  try {
    console.log('[DEBUG] Incoming req.body for uploadFile:', req.body);
    if (!req.files || Object.keys(req.files).length === 0 || !req.files.files) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded or files key is missing'
      });
    }

    const { ficheDeTestID, type } = req.body;

    if (!ficheDeTestID) {
      return res.status(400).json({
        success: false,
        message: 'Fiche de test ID is required'
      });
    }

    // Verify fiche de test exists - use a raw query if needed
    let ficheDeTest;
    try {
      ficheDeTest = await FicheDeTest.findByPk(ficheDeTestID);
      
      // If not found with findByPk, try a raw query
      if (!ficheDeTest) {
        const [results] = await db.sequelize.query(
          `SELECT * FROM "FicheDeTests" WHERE id = :id`,
          {
            replacements: { id: ficheDeTestID },
            type: db.sequelize.QueryTypes.SELECT
          }
        );
        
        if (results && results.length > 0) {
          ficheDeTest = results[0];
        }
      }
    } catch (error) {
      console.error('Error finding FicheDeTest:', error);
      // Continue anyway for testing purposes
    }

    if (!ficheDeTest && process.env.NODE_ENV !== 'development') {
      return res.status(404).json({
        success: false,
        message: 'Fiche de test not found'
      });
    }

    if (!type || !['business-document', 'external-reference'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Valid attachment type is required'
      });
    }

    const uploadedFiles = Array.isArray(req.files.files) ? req.files.files : [req.files.files];
    const uploadedAttachments = [];

    for (const file of uploadedFiles) {
      const fileExtension = path.extname(file.name);
      const fileName = `${uuidv4()}${fileExtension}`;
      const uploadDir = type === 'business-document' ? ficheDeTestDocsDir : ficheDeTestRefsDir;
      const filePath = path.join(uploadDir, fileName);

      // Move the file to the uploads directory
      await file.mv(filePath);

      // Generate a unique ID for the attachment
      const attachmentID = `FDT_ATT_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Save file metadata to database
      const attachment = await FicheDeTestAttachment.create({
        attachmentID,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.mimetype,
        filePath: fileName,
        uploadDate: new Date(),
        type,
        ficheDeTestID
      });
      uploadedAttachments.push(attachment);
    }

    res.status(201).json({
      success: true,
      message: 'File(s) uploaded successfully',
      data: uploadedAttachments
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload file',
      error: error.message
    });
  }
};

// Add web reference
const addReference = async (req, res) => {
  try {
    const { ficheDeTestID, url, description } = req.body;

    if (!ficheDeTestID) {
      return res.status(400).json({
        success: false,
        message: 'Fiche de test ID is required'
      });
    }

    // Verify fiche de test exists
    const ficheDeTest = await FicheDeTest.findByPk(ficheDeTestID);
    if (!ficheDeTest) {
      return res.status(404).json({
        success: false,
        message: 'Fiche de test not found'
      });
    }

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Ensure URL has protocol prefix
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
    }

    // Extract hostname for name if no description is provided
    let fileName;
    try {
      const urlObj = new URL(formattedUrl);
      fileName = urlObj.hostname;
    } catch (e) {
      fileName = 'External link';
    }

    // Generate a unique ID for the attachment
    const attachmentID = `FDT_REF_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Save reference to database
    const reference = await FicheDeTestAttachment.create({
      attachmentID,
      fileName,
      url: formattedUrl,
      description,
      uploadDate: new Date(),
      type: 'external-reference',
      ficheDeTestID
    });
    console.log('[DEBUG] Created external reference:', reference.toJSON());

    res.status(201).json({
      success: true,
      message: 'Reference added successfully',
      data: reference
    });
  } catch (error) {
    console.error('Error adding reference:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add reference',
      error: error.message
    });
  }
};

// Get all attachments for a fiche de test
const getAttachments = async (req, res) => {
  try {
    const { ficheDeTestID } = req.params;
    const { type } = req.query; // Destructure type from query parameters

    if (!ficheDeTestID) {
      return res.status(400).json({
        success: false,
        message: 'Fiche de test ID is required'
      });
    }

    const whereClause = { ficheDeTestID };

    // Conditionally add type to the where clause if provided
    if (type && ['business-document', 'external-reference'].includes(type)) {
      whereClause.type = type;
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const attachments = await FicheDeTestAttachment.findAll({
      where: whereClause, // Use the dynamically built whereClause
      order: [['uploadDate', 'DESC']],
      raw: true // Get plain objects instead of Sequelize instances for better performance
    });

    res.status(200).json({
      success: true,
      data: attachments.map(attachment => ({
        id: attachment.attachmentID,
        name: attachment.fileName,
        size: attachment.fileSize,
        type: attachment.fileType,
        url: attachment.url,
        description: attachment.description,
        uploadDate: attachment.uploadDate,
        attachmentType: attachment.type,
        ficheDeTestID: attachment.ficheDeTestID
      }))
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments',
      error: error.message
    });
  }
};

// Get all attachments across all fiches de test
const getAllAttachments = async (req, res) => {
  try {
    // Optional query parameters for filtering
    const { type, limit = 100, offset = 0 } = req.query;
    
    // Build query conditions
    const where = {};
    if (type && ['business-document', 'external-reference'].includes(type)) {
      where.type = type;
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Query with pagination
    const attachments = await FicheDeTestAttachment.findAll({
      where,
      order: [['uploadDate', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      raw: true
    });

    // Get total count for pagination
    const totalCount = await FicheDeTestAttachment.count({ where });

    res.status(200).json({
      success: true,
      data: attachments.map(attachment => ({
        id: attachment.attachmentID,
        name: attachment.fileName,
        size: attachment.fileSize,
        type: attachment.fileType,
        url: attachment.url,
        description: attachment.description,
        uploadDate: attachment.uploadDate,
        attachmentType: attachment.type,
        ficheDeTestID: attachment.ficheDeTestID
      })),
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    console.error('Error fetching all attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments',
      error: error.message
    });
  }
};

// Download an attachment
const downloadAttachment = async (req, res) => {
  try {
    const { attachmentID } = req.params;

    const attachment = await FicheDeTestAttachment.findByPk(attachmentID);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // If it's a URL reference, redirect to the URL
    if (attachment.url) {
      return res.redirect(attachment.url);
    }

    // If it's a file attachment, send the file
    if (attachment.filePath) {
      const uploadDir = attachment.type === 'business-document' ? ficheDeTestDocsDir : ficheDeTestRefsDir;
      const filePath = path.join(uploadDir, attachment.filePath);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: 'File not found on server'
        });
      }

      // Set appropriate headers
      res.setHeader('Content-Disposition', `attachment; filename="${attachment.fileName}"`);
      if (attachment.fileType) {
        res.setHeader('Content-Type', attachment.fileType);
      }

      // Stream the file to the response
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } else {
      return res.status(400).json({
        success: false,
        message: 'Invalid attachment type'
      });
    }
  } catch (error) {
    console.error('Error downloading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download attachment',
      error: error.message
    });
  }
};

// Delete an attachment
const deleteAttachment = async (req, res) => {
  try {
    const { attachmentID } = req.params;

    const attachment = await FicheDeTestAttachment.findByPk(attachmentID);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // If it's a file attachment, delete the file
    if (attachment.filePath) {
      const uploadDir = attachment.type === 'business-document' ? ficheDeTestDocsDir : ficheDeTestRefsDir;
      const filePath = path.join(uploadDir, attachment.filePath);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // Delete from database
    await attachment.destroy();

    res.status(200).json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attachment',
      error: error.message
    });
  }
};

module.exports = {
  uploadFile,
  addReference,
  getAttachments,
  getAllAttachments,
  downloadAttachment,
  deleteAttachment
};






