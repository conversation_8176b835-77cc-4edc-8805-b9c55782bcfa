import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Trash2, Plus, Loader2 } from "lucide-react";
import { useCustomOutletContext } from "../../edit-fiches-travail";
import { 
  getFichesDeTestByFicheDeTravailId, 
  createFicheDeTest, 
  updateFicheDeTest, 
  deleteFicheDeTest 
} from "@/services/fiche-de-test-service";
import { toast } from "sonner";

function FichesTestTab() {
  const navigate = useNavigate();
  const { fiche } = useCustomOutletContext();
  const abortControllerRef = useRef(null);

  // State
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [fichesTest, setFichesTest] = useState([]);
  const [editingFiche, setEditingFiche] = useState(null);
  const [formData, setFormData] = useState({
    titre: "",
    elementTrouve: false,
    commentaire: "",
    preuve: "",
    dateSignature: "",
    signe: false,
    responsable: ""
  });

  // Load fiches de test
  useEffect(() => {
    const fetchFichesTest = async () => {
      if (!fiche?.id) return;
      
      setIsLoading(true);
      try {
        // Cancel any existing request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        const response = await getFichesDeTestByFicheDeTravailId(fiche.id, abortControllerRef.current.signal);
        if (response && response.success) {
          setFichesTest(response.data || []);
        }
      } catch (error) {
        if (error.name !== 'CanceledError') {
          console.error('Error fetching fiches de test:', error);
          toast.error("Erreur lors du chargement des fiches de test");
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchFichesTest();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fiche?.id]);

  // Handlers
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddNew = () => {
    setEditingFiche(null);
    setFormData({
      titre: "",
      elementTrouve: false,
      commentaire: "",
      preuve: "",
      dateSignature: "",
      signe: false,
      responsable: ""
    });
    setIsDialogOpen(true);
  };

  const handleEdit = (ficheTest) => {
    setEditingFiche(ficheTest);
    setFormData({
      titre: ficheTest.titre || "",
      elementTrouve: ficheTest.elementTrouve || false,
      commentaire: ficheTest.commentaire || "",
      preuve: ficheTest.preuve || "",
      dateSignature: ficheTest.dateSignature ? new Date(ficheTest.dateSignature).toISOString().split('T')[0] : "",
      signe: ficheTest.signe || false,
      responsable: ficheTest.responsable || ""
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (ficheTest) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cette fiche de test ?")) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await deleteFicheDeTest(ficheTest.id);
      if (response && response.success) {
        toast.success("Fiche de test supprimée avec succès");
        // Refresh the list
        setFichesTest(prev => prev.filter(item => item.id !== ficheTest.id));
      } else {
        throw new Error(response?.message || "Erreur lors de la suppression");
      }
    } catch (error) {
      console.error('Error deleting fiche de test:', error);
      toast.error(error.message || "Erreur lors de la suppression de la fiche de test");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSave = async () => {
    if (!formData.titre.trim()) {
      toast.error("Le titre est requis");
      return;
    }

    if (!fiche?.id) {
      toast.error("ID de fiche de travail manquant");
      return;
    }

    setIsSaving(true);
    try {
      const ficheData = {
        ...formData,
        ficheDeTravailID: fiche.id,
        responsableID: 1, // TODO: Get current user ID
        dateSignature: formData.dateSignature || null
      };

      let response;
      if (editingFiche) {
        response = await updateFicheDeTest(editingFiche.id, ficheData);
      } else {
        response = await createFicheDeTest(ficheData);
      }

      if (response && response.success) {
        toast.success(editingFiche ? "Fiche de test mise à jour avec succès" : "Fiche de test créée avec succès");
        setIsDialogOpen(false);
        
        // Refresh the list
        const updatedResponse = await getFichesDeTestByFicheDeTravailId(fiche.id);
        if (updatedResponse && updatedResponse.success) {
          setFichesTest(updatedResponse.data || []);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      console.error('Error saving fiche de test:', error);
      toast.error(error.message || "Erreur lors de la sauvegarde de la fiche de test");
    } finally {
      setIsSaving(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR");
    } catch {
      return "N/A";
    }
  };

  const handleRowClick = (ficheTest) => {
    // Navigate to the edit page for this fiche de test
    navigate(`/audit/plans-daudit/fiches-test/${ficheTest.id}`);
  };

  if (!fiche) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-500"></div>
        <span className="ml-4 text-red-700">Chargement de la fiche de travail...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex justify-end mb-4">
        <Button 
          className="bg-[#F62D51] hover:bg-[#F62D51]/90" 
          onClick={handleAddNew}
          disabled={isLoading}
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau
        </Button>
      </div>
      
      <div className="overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="bg-gray-50 hover:bg-gray-100/50">
              <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom local</TableHead>
              <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Élément trouvé</TableHead>
              <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de signature</TableHead>
              <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Est signé</TableHead>
              <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Contient des noms</TableHead>
              <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Un deuxième exemplaire est stocké</TableHead>
              <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre de parties</TableHead>
              <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commentaires</TableHead>
              <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preuve</TableHead>
              <TableHead className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="divide-y divide-gray-200">
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={10} className="px-4 py-10 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span className="text-sm text-gray-500">Chargement...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : fichesTest.length > 0 ? (
              fichesTest.map((item) => (
                <TableRow 
                  key={item.id} 
                  className="hover:bg-gray-50/50 cursor-pointer"
                  onClick={() => handleRowClick(item)}
                >
                  <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                    {item.titre}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                    {item.elementTrouve ? "Oui" : "Non"}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                    {formatDate(item.dateSignature)}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm text-center whitespace-nowrap">
                    {item.signe ? "Oui" : "Non"}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm text-center whitespace-nowrap">
                    N/A
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm text-center whitespace-nowrap">
                    N/A
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm text-center whitespace-nowrap">
                    N/A
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                    {item.commentaire || "N/A"}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                    {item.preuve || "N/A"}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-sm text-center whitespace-nowrap">
                    <div className="flex items-center justify-center space-x-2" onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(item)}
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(item)}
                        disabled={isDeleting}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={10} className="px-4 py-10 text-center text-sm text-gray-500">
                  Aucun test
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Modal for Add/Edit */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingFiche ? "Modifier la fiche de test" : "Nouvel élément de test"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="titre" className="block text-sm font-medium text-gray-700">Nom *</label>
              <Input 
                id="titre" 
                name="titre"
                value={formData.titre} 
                onChange={handleInputChange} 
                placeholder="Nom" 
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Détenteur</label>
              <Input value={fiche.name} disabled className="bg-gray-100" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Annuler
            </Button>
            <Button 
              className="bg-[#F62D51] hover:bg-[#F62D51]/90" 
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sauvegarde...
                </>
              ) : (
                editingFiche ? "Modifier" : "Ajouter"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default FichesTestTab;